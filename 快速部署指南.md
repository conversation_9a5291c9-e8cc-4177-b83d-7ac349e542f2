# 基因检测AI专业顾问+销售教练 - 快速部署指南

## 🚀 推荐使用：修复版工作流

**文件：** `基因检测销售助理-修复版.json`

**修复内容：**
- ✅ 已修复AI幻觉问题
- ✅ 严格按照RAG查询结果回答
- ✅ 完整的参数配置
- ✅ 基于您的实际配置优化

## 📋 部署步骤

### 1. 导入工作流
1. 打开n8n管理界面
2. 点击"Import from file"
3. 选择 `基因检测销售助理-修复版.json`
4. 确认导入

### 2. 配置凭证
需要配置以下凭证：

#### DeepSeek API凭证
- **凭证名称：** DmxApi account
- **凭证ID：** wGIFZbERTYLkQ8BX
- **API Key：** 您的DeepSeek API密钥

#### Postgres数据库凭证
- **凭证名称：** Postgres account  
- **凭证ID：** mFqJLQjyKr6K0cls
- **配置：** 您的Postgres数据库连接信息

#### RAGFlow凭证
- **凭证名称：** ragflow Header Auth account
- **凭证ID：** Wog5gFcpfJ9pPhHc
- **Header Auth：** 您的RAGFlow访问凭证

#### Webhook认证凭证
- **凭证名称：** Header Auth account
- **凭证ID：** xIJfZnDKhDzjh06m
- **配置：** Webhook访问认证

### 3. 更新RAGFlow知识库
将以下文档上传到您的RAGFlow知识库：

```
销售话术库/
├── 01-开场话术模板.md
├── 02-异议处理策略.md
├── 03-专业知识科普话术.md
├── 04-客户心理分析与应对.md
└── 05-AI幻觉问题修复指南.md
```

### 4. 测试工作流
1. 激活工作流
2. 使用Webhook URL进行测试
3. 验证AI回答的准确性

## 🔧 关键配置说明

### AI Agent核心配置
```json
{
  "systemMessage": "你是基因检测专业顾问+销售教练...",
  "重要原则": [
    "严格按照RAGFlow查询结果回答",
    "必须先调用RAGFlow工具",
    "不能添加查询结果中没有的内容",
    "准确性是专业性的基础"
  ]
}
```

### Webhook配置
- **路径：** `gene-testing-advisor-fixed`
- **方法：** POST
- **认证：** Header Auth
- **响应模式：** Response Node

### 语言模型配置
- **主模型：** DeepSeek V3 (DMXAPI-HuoShan-DeepSeek-V3)
- **备用模型：** Qwen2.5-7B-Instruct
- **用途：** 主模型用于AI Agent，备用模型用于元数据处理

## ⚠️ 重要注意事项

### 1. AI幻觉问题预防
- 系统已强制要求先调用RAGFlow工具
- 严格约束只能使用查询结果中的信息
- 如遇不确定信息，会明确说明"需要进一步确认"

### 2. 检测内容准确性
**重要对照表：**
| 项目类型 | 是否包含TMB | 检测内容 |
|---------|------------|----------|
| 180基因系列 | ❌ 否 | 靶向+MSI+MMR+免疫正负向/超进展+遗传+化疗 |
| 1081基因系列 | ✅ 是 | 靶向+TMB+MSI+MMR+HLA+TNB+免疫正负向/超进展+遗传+化疗 |

### 3. 质量监控
- 定期检查AI回答准确性
- 监控RAGFlow工具调用情况
- 收集销售人员反馈

## 🎯 使用效果验证

### 测试场景1：肿瘤病理报告咨询
**输入：** "客户发了胆管癌病理报告，问要做什么基因检测？"

**期望输出格式：**
```
【专业解读】
根据胆管癌病理报告，建议做肝胆胰肿瘤180基因检测...

【推荐话术】
"根据您的病理报告显示胆管癌，我建议您考虑..."

【注意事项】
- 强调个性化治疗的重要性...

【可能异议及应对】
Q: "一定要做吗？"
A: "虽然不是必须的，但..."
```

### 测试场景2：项目内容确认
**输入：** "肝胆胰肿瘤180基因包含TMB检测吗？"

**期望输出：** 
明确回答"不包含"，并说明实际检测内容。

## 📞 技术支持

如遇到部署问题：
1. 检查所有凭证配置是否正确
2. 确认RAGFlow知识库内容已更新
3. 验证Webhook URL可访问性
4. 查看工作流执行日志

---

**🎉 部署完成后，您将拥有一个24小时不间断、准确可靠的基因检测AI专业顾问！**
