{"name": "基因检测销售助理", "nodes": [{"parameters": {"model": "DMXAPI-<PERSON><PERSON><PERSON><PERSON>-DeepSeek-V3", "options": {}}, "id": "0d129704-654f-4450-961f-0a05e07f897c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [928, 352], "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.sessionId }}"}, "id": "317fc51f-dc55-4ddc-88f0-d2953f8175d0", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [1104, 352], "notesInFlow": false, "credentials": {"postgres": {"id": "mFqJLQjyKr6K0cls", "name": "Postgres account"}}}, {"parameters": {"content": "## AI Agent with Webhook for Open WebUI", "height": 525, "width": 1596, "color": 6}, "id": "17c80a4d-aa3c-411e-bd95-b3c4fcdf1a2b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"options": {}}, "id": "4dcaccbb-f637-4af4-b530-dd7855498351", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1360, 80]}, {"parameters": {"httpMethod": "POST", "path": "invoke-n8n-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "a7d783e7-0464-4732-81d9-7dd298608db7", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [64, 160], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "image_url", "displayName": "image_url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1264, 352], "id": "f6e16c64-505b-4e80-a6a6-f9ed03ba54bf", "name": "Web Search Tool", "disabled": true}, {"parameters": {"content": "## Example Agent Tool", "height": 340, "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [928, 544], "id": "1e903e02-6544-414c-b21e-538059257352", "name": "Sticky Note7"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 640], "id": "a1550629-250e-407b-9076-8fc098f699f1", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}, "disabled": true}, {"parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [992, 640], "id": "05ff53a8-bfe2-4d98-8004-0b733e133b9d", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "60de6cdf-1147-41ac-9d2e-b76e4dc493dd", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1408, 640], "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}, "disabled": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.sessionId }}", "rightValue": "None", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "0731f392-10cb-4fad-b54a-9c447ac7f03d", "leftValue": "={{ $json.body.chatInput }}", "rightValue": "^### Task:", "operator": {"type": "string", "operation": "notRegex"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [272, 160], "id": "e1e05069-e4a4-4506-941e-1a62e7744dc8", "name": "If"}, {"parameters": {"model": {"__rl": true, "value": "Qwen/Qwen2.5-7B-Instruct", "mode": "list", "cachedResultName": "Qwen/Qwen2.5-7B-Instruct"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [560, 400], "id": "b28d3b48-2481-4e46-beb2-d0c5f8cd715f", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1152, 80], "id": "ac06d18e-6a92-4ba0-aea0-fc604bb29a60", "name": "Edit Fields (Set Output Field)"}, {"parameters": {"promptType": "define", "text": "={{ $json.body.chatInput }}", "options": {"systemMessage": "你是一位资深的基因检测销售助手，融合了专业遗传咨询师和顶尖销售专家的双重角色。\n\n**核心能力：**\n- 专业遗传咨询：解释基因检测原理、意义、局限性\n- 精准销售咨询：根据客户需求推荐最适合的检测方案\n- 个性化服务：提供详细、专业的咨询建议\n\n**工作流程：**\n1. 深度理解客户需求（年龄、性别、家族史、关注点等）\n2. 对于具体项目推荐、价格、技术参数等问题，优先使用RAGFlow工具查询公司资料\n3. 结合查询结果和专业知识，提供全面详细的建议\n4. 主动询问细节，确保推荐的精准性\n\n**回答标准：**\n- 每个回答至少200-300字，确保信息充分\n- 包含科学原理解释、临床意义、适用人群\n- 提供具体的项目名称、检测内容、价格区间\n- 说明检测流程、报告解读、后续服务\n- 主动提供相关建议和替代方案\n\n**调用RAGFlow MCP Client工具时机：**\n- 客户询问具体检测项目时\n- 需要价格和套餐信息时\n- 涉及技术参数和检测范围时\n- 需要最新的产品信息时\n\n记住：你不是简单的信息提供者，而是专业的基因检测咨询顾问。"}}, "id": "2a3cfcf2-089c-480a-909a-0c8a996bf3d3", "name": "Primary AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [672, 80]}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook').item.json.body.chatInput }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [464, 272], "id": "fe0d32fc-6944-45bf-a7c7-a48c016ee2dc", "name": "Open WebUI Metadata LLM"}, {"parameters": {"endpointUrl": "https://ragflow-mcp.23cc.cn/sse", "authentication": "headerAuth"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [1408, 352], "id": "fd40a7ef-b3fe-4520-94b8-e47020ebdd5b", "name": "RAGFlow MCP Client", "alwaysOutputData": false, "credentials": {"httpHeaderAuth": {"id": "Wog5gFcpfJ9pPhHc", "name": "ragflow Header Auth account"}}}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Primary AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Primary AI Agent", "type": "ai_memory", "index": 0}]]}, "Webhook": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "Primary AI Agent", "type": "ai_tool", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Primary AI Agent", "type": "main", "index": 0}], [{"node": "Open WebUI Metadata LLM", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Open WebUI Metadata LLM", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields (Set Output Field)": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Primary AI Agent": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "Open WebUI Metadata LLM": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "RAGFlow MCP Client": {"ai_tool": [[{"node": "Primary AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3ff10d7b-ffe6-412d-82bc-a1853cad3b41", "meta": {"templateCredsSetupCompleted": true, "instanceId": "94b5e24fbde64eff5632edea641ad06d51c77cebf6a861f48cb0018fd2a13b0b"}, "id": "SJTOtrXrZ6sFwnOZ", "tags": [{"createdAt": "2025-07-31T10:03:40.941Z", "updatedAt": "2025-07-31T10:03:40.941Z", "id": "TzUv8yezq8vpDJM1", "name": "八方基因"}]}