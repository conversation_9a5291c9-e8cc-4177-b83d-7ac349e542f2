{"name": "基因检测销售助理", "nodes": [{"parameters": {"model": "DMXAPI-<PERSON><PERSON><PERSON><PERSON>-DeepSeek-V3", "options": {}}, "id": "0d129704-654f-4450-961f-0a05e07f897c", "name": "OpenAI Chat Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [928, 352], "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.sessionId }}"}, "id": "317fc51f-dc55-4ddc-88f0-d2953f8175d0", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [1104, 352], "notesInFlow": false, "credentials": {"postgres": {"id": "mFqJLQjyKr6K0cls", "name": "Postgres account"}}}, {"parameters": {"content": "## AI Agent with Webhook for Open WebUI", "height": 525, "width": 1596, "color": 6}, "id": "17c80a4d-aa3c-411e-bd95-b3c4fcdf1a2b", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"options": {}}, "id": "4dcaccbb-f637-4af4-b530-dd7855498351", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1360, 80]}, {"parameters": {"httpMethod": "POST", "path": "invoke-n8n-agent", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "a7d783e7-0464-4732-81d9-7dd298608db7", "name": "Webhook", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [64, 160], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}}, {"parameters": {"name": "web_search", "description": "Call this tool to do an advanced web search based on a query you define.", "workflowId": {"__rl": true, "value": "={{ $workflow.id }}", "mode": "id"}, "workflowInputs": {"mappingMode": "defineBelow", "value": {"query": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('query', ``, 'string') }}"}, "matchingColumns": [], "schema": [{"id": "tool_type", "displayName": "tool_type", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}, {"id": "query", "displayName": "query", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string"}, {"id": "image_url", "displayName": "image_url", "required": false, "defaultMatch": false, "display": true, "canBeUsedToMatch": true, "type": "string", "removed": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}}, "type": "@n8n/n8n-nodes-langchain.toolWorkflow", "typeVersion": 2, "position": [1264, 352], "id": "f6e16c64-505b-4e80-a6a6-f9ed03ba54bf", "name": "Web Search Tool", "disabled": true}, {"parameters": {"content": "## Example Agent Tool", "height": 340, "width": 680, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [928, 544], "id": "1e903e02-6544-414c-b21e-538059257352", "name": "Sticky Note7"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/web/search?q={{ $('Tool Start').item.json.query }} }}&summary=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 640], "id": "a1550629-250e-407b-9076-8fc098f699f1", "name": "Brave Web Search", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}, "disabled": true}, {"parameters": {"workflowInputs": {"values": [{"name": "query"}]}}, "type": "n8n-nodes-base.executeWorkflowTrigger", "typeVersion": 1.1, "position": [992, 640], "id": "05ff53a8-bfe2-4d98-8004-0b733e133b9d", "name": "Tool Start"}, {"parameters": {"url": "=https://api.search.brave.com/res/v1/summarizer/search?key={{ $json.summarizer.key }}&entity_info=1", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "Accept-Encoding", "value": "gzip"}]}, "options": {}}, "id": "60de6cdf-1147-41ac-9d2e-b76e4dc493dd", "name": "Summarize Web Research", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1408, 640], "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}, "disabled": true}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.sessionId }}", "rightValue": "None", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "0731f392-10cb-4fad-b54a-9c447ac7f03d", "leftValue": "={{ $json.body.chatInput }}", "rightValue": "^### Task:", "operator": {"type": "string", "operation": "notRegex"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [272, 160], "id": "e1e05069-e4a4-4506-941e-1a62e7744dc8", "name": "If"}, {"parameters": {"model": {"__rl": true, "value": "Qwen/Qwen2.5-7B-Instruct", "mode": "list", "cachedResultName": "Qwen/Qwen2.5-7B-Instruct"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [560, 400], "id": "b28d3b48-2481-4e46-beb2-d0c5f8cd715f", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1152, 80], "id": "ac06d18e-6a92-4ba0-aea0-fc604bb29a60", "name": "Edit Fields (Set Output Field)"}, {"parameters": {"promptType": "define", "text": "={{ $json.body.chatInput }}", "options": {"systemMessage": "你是基因检测专业顾问+销售教练，为销售人员提供双重支持，帮助他们成为客户信赖的专业顾问。\n\n**🔬 专业顾问角色：**\n- 解读肿瘤病理报告：识别关键信息，推荐对应的靶向/免疫/化疗等检测项目\n- 分析遗传表型症状：根据患者症状和家族史，推荐合适的遗传基因检测项目\n- 专业知识科普：将复杂医学概念转化为客户易懂的语言\n- 检测价值阐述：解释检测的临床意义和对治疗的指导作用\n\n**💼 销售教练角色：**\n- 话术指导：根据客户情况和销售阶段，提供具体可用的对话模板\n- 异议处理：分析客户顾虑，提供专业的应对策略和话术\n- 心理分析：判断客户的购买意向和决策阶段\n- 成交指导：提供促成交易的技巧和时机把握\n\n**🎯 工作模式：**\n当销售人员向我咨询时，我会：\n1. **必须先调用RAGFlow工具**查询准确的项目信息\n2. **严格基于查询结果**提供专业知识支持\n3. 然后给出销售指导建议（话术模板/应对策略）\n4. 预测客户可能的疑问和标准应对方法\n\n**📋 标准输出格式：**\n【专业解读】- 医学知识的专业分析\n【推荐话术】- 具体可用的对话模板\n【注意事项】- 沟通中的要点和禁忌\n【可能异议及应对】- 客户疑问的预判和标准回答\n\n**🔍 调用RAGFlow工具时机：**\n- 需要具体检测项目信息时（**必须调用**）\n- 查询技术参数和检测范围时\n- 获取最新产品资料时\n- 寻找销售话术和案例时\n\n**⚠️ 重要原则：**\n1. **严格按照RAGFlow查询结果回答**，绝不能添加查询结果中没有的内容\n2. **检测内容必须与RAGFlow返回的完全一致**，不能推测或补充\n3. **如果RAGFlow中没有某个信息，明确说明\"需要进一步确认\"**\n4. **价格信息只能使用RAGFlow中的标准价格，不能自行估算**\n5. **特别注意：不同项目的检测内容不同，不能混淆**\n\n**💡 核心理念：**\n帮助销售人员既有专业深度，又有销售技巧，让客户感受到专业性的同时，自然而然地产生购买意愿。**准确性是专业性的基础**。"}}, "id": "2a3cfcf2-089c-480a-909a-0c8a996bf3d3", "name": "Primary AI Agent", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [672, 80]}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook').item.json.body.chatInput }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.5, "position": [464, 272], "id": "fe0d32fc-6944-45bf-a7c7-a48c016ee2dc", "name": "Open WebUI Metadata LLM"}, {"parameters": {"endpointUrl": "https://ragflow-mcp.23cc.cn/sse", "authentication": "headerAuth"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [1408, 352], "id": "fd40a7ef-b3fe-4520-94b8-e47020ebdd5b", "name": "RAGFlow MCP Client", "alwaysOutputData": false, "credentials": {"httpHeaderAuth": {"id": "Wog5gFcpfJ9pPhHc", "name": "ragflow Header Auth account"}}}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "Primary AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "Primary AI Agent", "type": "ai_memory", "index": 0}]]}, "Webhook": {"main": [[{"node": "If", "type": "main", "index": 0}]]}, "Web Search Tool": {"ai_tool": [[{"node": "Primary AI Agent", "type": "ai_tool", "index": 0}]]}, "Tool Start": {"main": [[{"node": "Brave Web Search", "type": "main", "index": 0}]]}, "Brave Web Search": {"main": [[{"node": "Summarize Web Research", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Primary AI Agent", "type": "main", "index": 0}], [{"node": "Open WebUI Metadata LLM", "type": "main", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Open WebUI Metadata LLM", "type": "ai_languageModel", "index": 0}]]}, "Edit Fields (Set Output Field)": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "Primary AI Agent": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "Open WebUI Metadata LLM": {"main": [[{"node": "Edit Fields (Set Output Field)", "type": "main", "index": 0}]]}, "RAGFlow MCP Client": {"ai_tool": [[{"node": "Primary AI Agent", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "3ff10d7b-ffe6-412d-82bc-a1853cad3b41", "meta": {"templateCredsSetupCompleted": true, "instanceId": "94b5e24fbde64eff5632edea641ad06d51c77cebf6a861f48cb0018fd2a13b0b"}, "id": "SJTOtrXrZ6sFwnOZ", "tags": [{"createdAt": "2025-07-31T10:03:40.941Z", "updatedAt": "2025-07-31T10:03:40.941Z", "id": "TzUv8yezq8vpDJM1", "name": "八方基因"}]}