{"name": "基因检测AI专业顾问+销售教练（修复版）", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.sessionId }}"}, "id": "a57f5753-0945-4cea-ad23-af96de4275d7", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-208, 192], "notesInFlow": false, "credentials": {"postgres": {"id": "mFqJLQjyKr6K0cls", "name": "Postgres account"}}}, {"parameters": {"content": "## 基因检测AI专业顾问+销售教练（修复版）\n\n✅ 已修复AI幻觉问题\n为销售人员提供24小时专业支持：\n- 🔬 专业知识：病理报告解读、表型分析\n- 💼 销售指导：话术模板、异议处理\n- 🎯 严格准确：只基于RAG查询结果回答\n- ⚠️ 核心原则：准确性是专业性的基础", "height": 400, "width": 600, "color": 6}, "id": "a09387f5-f252-4a76-93ff-36358d04d8e4", "name": "工作流说明", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-960, 16]}, {"parameters": {"options": {}}, "id": "898415d5-d082-48f1-9940-b6070faa86ac", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [352, 80]}, {"parameters": {"httpMethod": "POST", "path": "gene-testing-advisor-fixed", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "32db8d3d-c28b-4e32-92c1-7b2595aba098", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-784, 224], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.sessionId }}", "rightValue": "None", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "0731f392-10cb-4fad-b54a-9c447ac7f03d", "leftValue": "={{ $json.body.chatInput }}", "rightValue": "^### Task:", "operator": {"type": "string", "operation": "notRegex"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-560, 224], "id": "78f6f6f0-af90-4748-90c9-7a59b354a1b0", "name": "Input Validation"}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [128, 80], "id": "2a06790f-5407-461a-932e-58ec395a2561", "name": "Format Output"}, {"parameters": {"promptType": "define", "text": "={{ $json.body.chatInput }}", "options": {"systemMessage": "你是基因检测专业顾问+销售教练，为销售人员提供双重支持，帮助他们成为客户信赖的专业顾问。\n\n**🔬 专业顾问角色：**\n- 解读肿瘤病理报告：识别关键信息，推荐对应的靶向/免疫/化疗等检测项目\n- 分析遗传表型症状：根据患者症状和家族史，推荐合适的遗传基因检测项目\n- 专业知识科普：将复杂医学概念转化为客户易懂的语言\n- 检测价值阐述：解释检测的临床意义和对治疗的指导作用\n\n**💼 销售教练角色：**\n- 话术指导：根据客户情况和销售阶段，提供具体可用的对话模板\n- 异议处理：分析客户顾虑，提供专业的应对策略和话术\n- 心理分析：判断客户的购买意向和决策阶段\n- 成交指导：提供促成交易的技巧和时机把握\n\n**🎯 工作模式：**\n当销售人员向我咨询时，我会：\n1. **必须先调用RAGFlow工具**查询准确的项目信息\n2. **严格基于查询结果**提供专业知识支持\n3. 然后给出销售指导建议（话术模板/应对策略）\n4. 预测客户可能的疑问和标准应对方法\n\n**📋 标准输出格式：**\n【专业解读】- 医学知识的专业分析\n【推荐话术】- 具体可用的对话模板\n【注意事项】- 沟通中的要点和禁忌\n【可能异议及应对】- 客户疑问的预判和标准回答\n\n**🔍 调用RAGFlow工具时机：**\n- 需要具体检测项目信息时（**必须调用**）\n- 查询技术参数和检测范围时\n- 获取最新产品资料时\n- 寻找销售话术和案例时\n\n**⚠️ 重要原则：**\n1. **严格按照RAGFlow查询结果回答**，绝不能添加查询结果中没有的内容\n2. **检测内容必须与RAGFlow返回的完全一致**，不能推测或补充\n3. **如果RAGFlow中没有某个信息，明确说明\"需要进一步确认\"**\n4. **价格信息只能使用RAGFlow中的标准价格，不能自行估算**\n5. **特别注意：不同项目的检测内容不同，不能混淆**\n\n**💡 核心理念：**\n帮助销售人员既有专业深度，又有销售技巧，让客户感受到专业性的同时，自然而然地产生购买意愿。**准确性是专业性的基础**。"}}, "id": "a2d29d75-e8d3-45af-979d-ccf2e93da346", "name": "AI专业顾问+销售教练", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-304, -32]}, {"parameters": {"endpointUrl": "https://ragflow-mcp.23cc.cn/sse", "authentication": "headerAuth"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [-16, 192], "id": "5966cd9c-0dda-4b19-b334-1ea0f6f185f8", "name": "RAGFlow Knowledge Base", "alwaysOutputData": false, "credentials": {"httpHeaderAuth": {"id": "Wog5gFcpfJ9pPhHc", "name": "ragflow Header Auth account"}}}, {"parameters": {"model": {"__rl": true, "value": "Qwen/Qwen2.5-7B-Instruct", "mode": "list", "cachedResultName": "Qwen/Qwen2.5-7B-Instruct"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-384, 576], "id": "bba99d29-3243-440a-8728-2d2849539855", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook Trigger').item.json.body.chatInput }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-304, 368], "id": "603b130b-625e-4996-a187-6c1c0c4d4084", "name": "打开 WebUI 元数据 LLM"}, {"parameters": {"model": "DMXAPI-<PERSON><PERSON><PERSON><PERSON>-DeepSeek-V3", "options": {}}, "id": "347ab941-38f1-4f35-a879-f9569d25c6b6", "name": "Language Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-336, 192], "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}], "pinData": {}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "AI专业顾问+销售教练", "type": "ai_memory", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Input Validation", "type": "main", "index": 0}]]}, "Input Validation": {"main": [[{"node": "AI专业顾问+销售教练", "type": "main", "index": 0}], [{"node": "打开 WebUI 元数据 LLM", "type": "main", "index": 0}]]}, "Format Output": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "AI专业顾问+销售教练": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "RAGFlow Knowledge Base": {"ai_tool": [[{"node": "AI专业顾问+销售教练", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "打开 WebUI 元数据 LLM", "type": "ai_languageModel", "index": 0}]]}, "Language Model": {"ai_languageModel": [[{"node": "AI专业顾问+销售教练", "type": "ai_languageModel", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "fixed-ai-hallucination-v1", "meta": {"templateCredsSetupCompleted": true, "instanceId": "gene-testing-ai-advisor-fixed"}, "id": "GeneTestingAdvisorFixed", "tags": [{"createdAt": "2025-01-03T00:00:00.000Z", "updatedAt": "2025-01-03T00:00:00.000Z", "id": "FixedWorkflow", "name": "基因检测AI顾问修复版"}]}