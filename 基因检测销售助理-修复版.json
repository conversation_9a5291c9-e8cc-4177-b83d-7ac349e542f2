{"name": "基因检测AI专业顾问+销售教练（修复版）", "nodes": [{"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.sessionId }}"}, "id": "a57f5753-0945-4cea-ad23-af96de4275d7", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [-208, 192], "notesInFlow": false, "credentials": {"postgres": {"id": "mFqJLQjyKr6K0cls", "name": "Postgres account"}}}, {"parameters": {"content": "## 基因检测AI专业顾问+销售教练（修复版）\n\n✅ 已修复AI幻觉问题\n为销售人员提供24小时专业支持：\n- 🔬 专业知识：病理报告解读、表型分析\n- 💼 销售指导：话术模板、异议处理\n- 🎯 严格准确：只基于RAG查询结果回答\n- ⚠️ 核心原则：准确性是专业性的基础", "height": 400, "width": 600, "color": 6}, "id": "a09387f5-f252-4a76-93ff-36358d04d8e4", "name": "工作流说明", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-960, 16]}, {"parameters": {"options": {}}, "id": "898415d5-d082-48f1-9940-b6070faa86ac", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [352, 80]}, {"parameters": {"httpMethod": "POST", "path": "gene-testing-advisor-fixed", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "32db8d3d-c28b-4e32-92c1-7b2595aba098", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-784, 224], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.sessionId }}", "rightValue": "None", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "0731f392-10cb-4fad-b54a-9c447ac7f03d", "leftValue": "={{ $json.body.chatInput }}", "rightValue": "^### Task:", "operator": {"type": "string", "operation": "notRegex"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [-560, 224], "id": "78f6f6f0-af90-4748-90c9-7a59b354a1b0", "name": "Input Validation"}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [128, 80], "id": "2a06790f-5407-461a-932e-58ec395a2561", "name": "Format Output"}, {"parameters": {"promptType": "define", "text": "={{ $json.body.chatInput }}", "options": {"systemMessage": "你是基因检测专业顾问+销售教练，为销售人员提供双重支持，帮助他们成为客户信赖的专业顾问。\n\n**🔬 专业顾问角色：**\n- 深度解读病理报告：不仅识别关键信息，更要分析临床意义和治疗指导价值\n- 专业知识科普：将复杂医学概念转化为客户易懂的语言，建立专业权威感\n- 个性化方案推荐：基于具体病情特点，推荐最适合的检测项目组合\n- 价值阐述：从治疗效果、时间成本、经济效益等多角度说明检测价值\n\n**💼 销售教练角色：**\n- 情感共鸣：理解客户焦虑，提供专业安慰和希望\n- 需求创造：通过专业分析，让客户认识到检测的必要性和紧迫性\n- 话术指导：提供具体可用的对话模板，包含开场、价值呈现、异议处理\n- 成交技巧：把握客户心理，提供促成决策的策略和时机\n\n**🎯 工作模式：**\n当销售人员向我咨询时，我会：\n1. **必须先调用RAGFlow工具**查询准确的项目信息\n2. **基于查询结果**进行深度专业分析，但可以结合医学常识进行解读\n3. 设计完整的销售策略，包含专业解读、话术模板、异议处理\n4. 提供具体可执行的沟通方案\n\n**📋 标准输出格式：**\n【专业解读】- 深度的医学分析和临床意义解读\n【推荐话术】- 分步骤的完整对话模板\n【注意事项】- 沟通技巧和专业要点\n【可能异议及应对】- 详细的异议处理策略\n\n**🔍 调用RAGFlow工具时机：**\n- 需要具体检测项目信息时（**必须调用**）\n- 查询价格和技术参数时\n- 获取检测内容和临床应用时\n\n**⚠️ 准确性原则：**\n1. **检测项目信息必须基于RAGFlow查询结果**，不能添加查询中没有的内容\n2. **价格信息严格使用RAGFlow中的数据**，不能自行估算\n3. **检测内容描述必须与查询结果一致**，特别注意不同项目的差异\n4. **如果某个信息在RAGFlow中没有，明确说明\"需要进一步确认\"**\n\n**💡 回答风格：**\n- **专业而温暖**：既有医学专业深度，又有人文关怀\n- **详细而实用**：提供具体可用的话术和策略\n- **逻辑清晰**：从专业分析到销售策略，层层递进\n- **准确可靠**：所有项目信息严格基于RAGFlow查询结果\n\n**🎯 核心目标：**\n帮助销售人员成为客户信赖的专业顾问，通过准确的信息和专业的服务，自然而然地促成成交。准确性是专业性的基础，专业性是信任的前提。"}}, "id": "a2d29d75-e8d3-45af-979d-ccf2e93da346", "name": "AI专业顾问+销售教练", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [-304, -32]}, {"parameters": {"endpointUrl": "https://ragflow-mcp.23cc.cn/sse", "authentication": "headerAuth"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [-16, 192], "id": "5966cd9c-0dda-4b19-b334-1ea0f6f185f8", "name": "RAGFlow Knowledge Base", "alwaysOutputData": false, "credentials": {"httpHeaderAuth": {"id": "Wog5gFcpfJ9pPhHc", "name": "ragflow Header Auth account"}}}, {"parameters": {"model": {"__rl": true, "value": "Qwen/Qwen2.5-7B-Instruct", "mode": "list", "cachedResultName": "Qwen/Qwen2.5-7B-Instruct"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [-384, 576], "id": "bba99d29-3243-440a-8728-2d2849539855", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"promptType": "define", "text": "={{ $('Webhook Trigger').item.json.body.chatInput }}"}, "type": "@n8n/n8n-nodes-langchain.chainLlm", "typeVersion": 1.7, "position": [-304, 368], "id": "603b130b-625e-4996-a187-6c1c0c4d4084", "name": "打开 WebUI 元数据 LLM"}, {"parameters": {"model": "DMXAPI-<PERSON><PERSON><PERSON><PERSON>-DeepSeek-V3", "options": {}}, "id": "347ab941-38f1-4f35-a879-f9569d25c6b6", "name": "Language Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [-336, 192], "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}], "pinData": {}, "connections": {"Postgres Chat Memory": {"ai_memory": [[{"node": "AI专业顾问+销售教练", "type": "ai_memory", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Input Validation", "type": "main", "index": 0}]]}, "Input Validation": {"main": [[{"node": "AI专业顾问+销售教练", "type": "main", "index": 0}], [{"node": "打开 WebUI 元数据 LLM", "type": "main", "index": 0}]]}, "Format Output": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "AI专业顾问+销售教练": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "RAGFlow Knowledge Base": {"ai_tool": [[{"node": "AI专业顾问+销售教练", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "打开 WebUI 元数据 LLM", "type": "ai_languageModel", "index": 0}]]}, "Language Model": {"ai_languageModel": [[{"node": "AI专业顾问+销售教练", "type": "ai_languageModel", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1"}, "versionId": "fixed-ai-hallucination-v1", "meta": {"templateCredsSetupCompleted": true, "instanceId": "gene-testing-ai-advisor-fixed"}, "id": "GeneTestingAdvisorFixed", "tags": [{"createdAt": "2025-01-03T00:00:00.000Z", "updatedAt": "2025-01-03T00:00:00.000Z", "id": "FixedWorkflow", "name": "基因检测AI顾问修复版"}]}