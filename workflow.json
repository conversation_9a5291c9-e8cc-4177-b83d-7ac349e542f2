{"name": "基因检测AI专业顾问+销售教练", "nodes": [{"parameters": {"model": "DMXAPI-<PERSON><PERSON><PERSON><PERSON>-DeepSeek-V3", "options": {}}, "id": "0d129704-654f-4450-961f-0a05e07f897c", "name": "DeepSeek V3 Language Model", "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [928, 352], "credentials": {"openAiApi": {"id": "wGIFZbERTYLkQ8BX", "name": "DmxApi account"}}}, {"parameters": {"sessionIdType": "customKey", "sessionKey": "={{ $json.body.sessionId }}"}, "id": "317fc51f-dc55-4ddc-88f0-d2953f8175d0", "name": "Postgres Chat Memory", "type": "@n8n/n8n-nodes-langchain.memoryPostgresChat", "typeVersion": 1, "position": [1104, 352], "notesInFlow": false, "credentials": {"postgres": {"id": "mFqJLQjyKr6K0cls", "name": "Postgres account"}}}, {"parameters": {"content": "## 基因检测AI专业顾问+销售教练\n\n为销售人员提供24小时专业支持：\n- 🔬 专业知识：病理报告解读、表型分析\n- 💼 销售指导：话术模板、异议处理\n- 🎯 个性化服务：根据客户情况提供针对性建议", "height": 525, "width": 1596, "color": 6}, "id": "17c80a4d-aa3c-411e-bd95-b3c4fcdf1a2b", "name": "工作流说明", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"options": {}}, "id": "4dcaccbb-f637-4af4-b530-dd7855498351", "name": "Respond to Webhook", "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1.1, "position": [1360, 80]}, {"parameters": {"httpMethod": "POST", "path": "gene-testing-advisor", "authentication": "headerAuth", "responseMode": "responseNode", "options": {}}, "id": "a7d783e7-0464-4732-81d9-7dd298608db7", "name": "Webhook Trigger", "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [64, 160], "webhookId": "7f8a71dd-b98a-4c87-aa3c-c0f7c3b63535", "credentials": {"httpHeaderAuth": {"id": "xIJfZnDKhDzjh06m", "name": "Header Auth account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "f5ebbd4f-6549-4a31-b3f8-eee7634dc439", "leftValue": "={{ $json.body.sessionId }}", "rightValue": "None", "operator": {"type": "string", "operation": "notEquals"}}, {"id": "0731f392-10cb-4fad-b54a-9c447ac7f03d", "leftValue": "={{ $json.body.chatInput }}", "rightValue": "^### Task:", "operator": {"type": "string", "operation": "notRegex"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [272, 160], "id": "e1e05069-e4a4-4506-941e-1a62e7744dc8", "name": "Input Validation"}, {"parameters": {"assignments": {"assignments": [{"id": "d264444f-c01a-4fa0-86a4-c0bf0e4c8537", "name": "output", "value": "={{ $json.output || $json.text }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1152, 80], "id": "ac06d18e-6a92-4ba0-aea0-fc604bb29a60", "name": "Format Output"}, {"parameters": {"promptType": "define", "text": "={{ $json.body.chatInput }}", "options": {"systemMessage": "你是基因检测专业顾问+销售教练，为销售人员提供双重支持，帮助他们成为客户信赖的专业顾问。\n\n**🔬 专业顾问角色：**\n- 解读肿瘤病理报告：识别关键信息，推荐对应的靶向/免疫/化疗等检测项目\n- 分析遗传表型症状：根据患者症状和家族史，推荐合适的遗传基因检测项目\n- 专业知识科普：将复杂医学概念转化为客户易懂的语言\n- 检测价值阐述：解释检测的临床意义和对治疗的指导作用\n\n**💼 销售教练角色：**\n- 话术指导：根据客户情况和销售阶段，提供具体可用的对话模板\n- 异议处理：分析客户顾虑，提供专业的应对策略和话术\n- 心理分析：判断客户的购买意向和决策阶段\n- 成交指导：提供促成交易的技巧和时机把握\n\n**🎯 工作模式：**\n当销售人员向我咨询时，我会：\n1. **必须先调用RAGFlow工具**查询准确的项目信息\n2. **严格基于查询结果**提供专业知识支持\n3. 然后给出销售指导建议（话术模板/应对策略）\n4. 预测客户可能的疑问和标准应对方法\n\n**📋 标准输出格式：**\n【专业解读】- 医学知识的专业分析\n【推荐话术】- 具体可用的对话模板\n【注意事项】- 沟通中的要点和禁忌\n【可能异议及应对】- 客户疑问的预判和标准回答\n\n**🔍 调用RAGFlow工具时机：**\n- 需要具体检测项目信息时（**必须调用**）\n- 查询技术参数和检测范围时\n- 获取最新产品资料时\n- 寻找销售话术和案例时\n\n**⚠️ 重要原则：**\n1. **严格按照RAGFlow查询结果回答**，绝不能添加查询结果中没有的内容\n2. **检测内容必须与RAGFlow返回的完全一致**，不能推测或补充\n3. **如果RAGFlow中没有某个信息，明确说明\"需要进一步确认\"**\n4. **价格信息只能使用RAGFlow中的标准价格，不能自行估算**\n5. **特别注意：不同项目的检测内容不同，不能混淆**\n\n**💡 核心理念：**\n帮助销售人员既有专业深度，又有销售技巧，让客户感受到专业性的同时，自然而然地产生购买意愿。**准确性是专业性的基础**。"}}, "id": "2a3cfcf2-089c-480a-909a-0c8a996bf3d3", "name": "AI专业顾问+销售教练", "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.6, "position": [672, 80]}, {"parameters": {"endpointUrl": "https://ragflow-mcp.23cc.cn/sse", "authentication": "headerAuth"}, "type": "@n8n/n8n-nodes-langchain.mcpClientTool", "typeVersion": 1.1, "position": [1408, 352], "id": "fd40a7ef-b3fe-4520-94b8-e47020ebdd5b", "name": "RAGFlow Knowledge Base", "alwaysOutputData": false, "credentials": {"httpHeaderAuth": {"id": "Wog5gFcpfJ9pPhHc", "name": "ragflow Header Auth account"}}}], "pinData": {}, "connections": {"DeepSeek V3 Language Model": {"ai_languageModel": [[{"node": "AI专业顾问+销售教练", "type": "ai_languageModel", "index": 0}]]}, "Postgres Chat Memory": {"ai_memory": [[{"node": "AI专业顾问+销售教练", "type": "ai_memory", "index": 0}]]}, "Webhook Trigger": {"main": [[{"node": "Input Validation", "type": "main", "index": 0}]]}, "Input Validation": {"main": [[{"node": "AI专业顾问+销售教练", "type": "main", "index": 0}]]}, "Format Output": {"main": [[{"node": "Respond to Webhook", "type": "main", "index": 0}]]}, "AI专业顾问+销售教练": {"main": [[{"node": "Format Output", "type": "main", "index": 0}]]}, "RAGFlow Knowledge Base": {"ai_tool": [[{"node": "AI专业顾问+销售教练", "type": "ai_tool", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "optimized-gene-testing-advisor-v2", "meta": {"templateCredsSetupCompleted": true, "instanceId": "gene-testing-ai-advisor-coach"}, "id": "GeneTestingAdvisorCoach", "tags": [{"createdAt": "2025-01-03T00:00:00.000Z", "updatedAt": "2025-01-03T00:00:00.000Z", "id": "OptimizedWorkflow", "name": "基因检测AI顾问"}]}